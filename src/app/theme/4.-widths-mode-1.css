/* 4.-widths-mode-1.css */
:root {
	--paragraph-max-width: var(--Spacing-180-(720px));
	--width-2xl: var(--Spacing-256-(1,024px));
	--width-3xl: var(--Spacing-320-(1,280px));
	--width-4xl: var(--Spacing-360-(1,440px));
	--width-5xl: var(--Spacing-400-(1,600px));
	--width-6xl: var(--Spacing-480-(1,920px));
	--width-lg: var(--Spacing-160-(640px));
	--width-md: var(--Spacing-140-(560px));
	--width-sm: var(--Spacing-120-(480px));
	--width-xl: var(--Spacing-192-(768px));
	--width-xs: var(--Spacing-96-(384px));
	--width-xxs: var(--Spacing-80-(320px));
}
