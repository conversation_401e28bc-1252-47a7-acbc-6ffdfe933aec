/* 1.-color-modes-light-mode.css */
:root {
	--Colors-Background-bg-active: var(--Colors-Gray-(light-mode)-50);
	--Colors-Background-bg-brand-primary: var(--Colors-Brand-50);
	--Colors-Background-bg-brand-primary_alt: var(--Colors-Brand-50);
	--Colors-Background-bg-brand-secondary: var(--Colors-Brand-100);
	--Colors-Background-bg-brand-section: var(--Colors-Brand-800);
	--Colors-Background-bg-brand-section_subtle: var(--Colors-Brand-700);
	--Colors-Background-bg-brand-solid: var(--Colors-Brand-600);
	--Colors-Background-bg-brand-solid_hover: var(--Colors-Brand-700);
	--Colors-Background-bg-disabled: var(--Colors-Gray-(light-mode)-100);
	--Colors-Background-bg-disabled_subtle: var(--Colors-Gray-(light-mode)-50);
	--Colors-Background-bg-error-primary: var(--Colors-Error-50);
	--Colors-Background-bg-error-secondary: var(--Colors-Error-100);
	--Colors-Background-bg-error-solid: var(--Colors-Error-600);
	--Colors-Background-bg-error-solid_hover: var(--Colors-Error-700);
	--Colors-Background-bg-overlay: var(--Colors-Gray-(light-mode)-950);
	--Colors-Background-bg-primary: var(--Colors-Base-white);
	--Colors-Background-bg-primary-solid: var(--Colors-Gray-(light-mode)-950);
	--Colors-Background-bg-primary_alt: var(--Colors-Base-white);
	--Colors-Background-bg-primary_hover: var(--Colors-Gray-(light-mode)-50);
	--Colors-Background-bg-quaternary: var(--Colors-Gray-(light-mode)-200);
	--Colors-Background-bg-secondary: var(--Colors-Gray-(light-mode)-50);
	--Colors-Background-bg-secondary-solid: var(--Colors-Gray-(light-mode)-600);
	--Colors-Background-bg-secondary_alt: var(--Colors-Gray-(light-mode)-50);
	--Colors-Background-bg-secondary_hover: var(--Colors-Gray-(light-mode)-100);
	--Colors-Background-bg-secondary_subtle: var(--Colors-Gray-(light-mode)-25);
	--Colors-Background-bg-success-primary: var(--Colors-Success-50);
	--Colors-Background-bg-success-secondary: var(--Colors-Success-100);
	--Colors-Background-bg-success-solid: var(--Colors-Success-600);
	--Colors-Background-bg-tertiary: var(--Colors-Gray-(light-mode)-100);
	--Colors-Background-bg-warning-primary: var(--Colors-Warning-50);
	--Colors-Background-bg-warning-secondary: var(--Colors-Warning-100);
	--Colors-Background-bg-warning-solid: var(--Colors-Warning-600);
	--Colors-Border-border-brand: var(--Colors-Brand-500);
	--Colors-Border-border-brand_alt: var(--Colors-Brand-600);
	--Colors-Border-border-disabled: var(--Colors-Gray-(light-mode)-300);
	--Colors-Border-border-disabled_subtle: var(--Colors-Gray-(light-mode)-200);
	--Colors-Border-border-error: var(--Colors-Error-500);
	--Colors-Border-border-error_subtle: var(--Colors-Error-300);
	--Colors-Border-border-primary: var(--Colors-Gray-(light-mode)-300);
	--Colors-Border-border-secondary: var(--Colors-Gray-(light-mode)-200);
	--Colors-Border-border-secondary_alt: rgb(0 0 0 / 8%);
	--Colors-Border-border-tertiary: var(--Colors-Gray-(light-mode)-100);
	--Colors-Effects-Focus-rings-focus-ring: var(--Colors-Brand-500);
	--Colors-Effects-Focus-rings-focus-ring-error: var(--Colors-Error-500);
	--Colors-Effects-Portfolio-mockups-shadow-grid-md: rgb(10 13 18 / 8%);
	--Colors-Effects-Portfolio-mockups-shadow-main-centre-lg: rgb(10 13 18 / 18%);
	--Colors-Effects-Portfolio-mockups-shadow-main-centre-md: rgb(10 13 18 / 14%);
	--Colors-Effects-Portfolio-mockups-shadow-overlay-lg: rgb(10 13 18 / 12%);
	--Colors-Effects-Shadows-shadow-2xl_01: rgb(10 13 18 / 18%);
	--Colors-Effects-Shadows-shadow-2xl_02: rgb(10 13 18 / 4%);
	--Colors-Effects-Shadows-shadow-3xl_01: rgb(10 13 18 / 14%);
	--Colors-Effects-Shadows-shadow-3xl_02: rgb(10 13 18 / 4%);
	--Colors-Effects-Shadows-shadow-lg_01: rgb(10 13 18 / 8%);
	--Colors-Effects-Shadows-shadow-lg_02: rgb(10 13 18 / 3%);
	--Colors-Effects-Shadows-shadow-lg_03: rgb(10 13 18 / 4%);
	--Colors-Effects-Shadows-shadow-md_01: rgb(10 13 18 / 10%);
	--Colors-Effects-Shadows-shadow-md_02: rgb(10 13 18 / 6%);
	--Colors-Effects-Shadows-shadow-skeumorphic-inner: rgb(10 13 18 / 5%);
	--Colors-Effects-Shadows-shadow-skeumorphic-inner-border: rgb(10 13 18 / 18%);
	--Colors-Effects-Shadows-shadow-sm_01: rgb(10 13 18 / 10%);
	--Colors-Effects-Shadows-shadow-sm_02: rgb(10 13 18 / 10%);
	--Colors-Effects-Shadows-shadow-xl_01: rgb(10 13 18 / 8%);
	--Colors-Effects-Shadows-shadow-xl_02: rgb(10 13 18 / 3%);
	--Colors-Effects-Shadows-shadow-xl_03: rgb(10 13 18 / 4%);
	--Colors-Effects-Shadows-shadow-xs: rgb(10 13 18 / 5%);
	--Colors-Foreground-fg-brand-primary-(600): var(--Colors-Brand-600);
	--Colors-Foreground-fg-brand-primary_alt: var(--Colors-Foreground-fg-brand-primary-(600));
	--Colors-Foreground-fg-brand-secondary-(500): var(--Colors-Brand-500);
	--Colors-Foreground-fg-brand-secondary_alt: var(--Colors-Foreground-fg-brand-secondary-(500));
	--Colors-Foreground-fg-brand-secondary_hover: var(--Colors-Brand-600);
	--Colors-Foreground-fg-disabled: var(--Colors-Gray-(light-mode)-400);
	--Colors-Foreground-fg-disabled_subtle: var(--Colors-Gray-(light-mode)-300);
	--Colors-Foreground-fg-error-primary: var(--Colors-Error-600);
	--Colors-Foreground-fg-error-secondary: var(--Colors-Error-500);
	--Colors-Foreground-fg-primary-(900): var(--Colors-Gray-(light-mode)-900);
	--Colors-Foreground-fg-quaternary-(400): var(--Colors-Gray-(light-mode)-400);
	--Colors-Foreground-fg-quaternary_hover: var(--Colors-Gray-(light-mode)-500);
	--Colors-Foreground-fg-secondary-(700): var(--Colors-Gray-(light-mode)-700);
	--Colors-Foreground-fg-secondary_hover: var(--Colors-Gray-(light-mode)-800);
	--Colors-Foreground-fg-success-primary: var(--Colors-Success-600);
	--Colors-Foreground-fg-success-secondary: var(--Colors-Success-500);
	--Colors-Foreground-fg-tertiary-(600): var(--Colors-Gray-(light-mode)-600);
	--Colors-Foreground-fg-tertiary_hover: var(--Colors-Gray-(light-mode)-700);
	--Colors-Foreground-fg-warning-primary: var(--Colors-Warning-600);
	--Colors-Foreground-fg-warning-secondary: var(--Colors-Warning-500);
	--Colors-Foreground-fg-white: var(--Colors-Base-white);
	--Colors-Text-text-brand-primary-(900): var(--Colors-Brand-900);
	--Colors-Text-text-brand-secondary-(700): var(--Colors-Brand-700);
	--Colors-Text-text-brand-tertiary-(600): var(--Colors-Brand-600);
	--Colors-Text-text-brand-tertiary_alt: var(--Colors-Brand-600);
	--Colors-Text-text-disabled: var(--Colors-Gray-(light-mode)-500);
	--Colors-Text-text-error-primary-(600): var(--Colors-Error-600);
	--Colors-Text-text-error-primary_hover: var(--Colors-Error-700);
	--Colors-Text-text-placeholder: var(--Colors-Gray-(light-mode)-500);
	--Colors-Text-text-placeholder_subtle: var(--Colors-Gray-(light-mode)-300);
	--Colors-Text-text-primary-(900): var(--Colors-Gray-(light-mode)-900);
	--Colors-Text-text-primary_on-brand: var(--Colors-Base-white);
	--Colors-Text-text-quaternary-(500): var(--Colors-Gray-(light-mode)-500);
	--Colors-Text-text-quaternary_on-brand: var(--Colors-Brand-300);
	--Colors-Text-text-secondary-(700): var(--Colors-Gray-(light-mode)-700);
	--Colors-Text-text-secondary_hover: var(--Colors-Gray-(light-mode)-800);
	--Colors-Text-text-secondary_on-brand: var(--Colors-Brand-200);
	--Colors-Text-text-success-primary-(600): var(--Colors-Success-600);
	--Colors-Text-text-tertiary-(600): var(--Colors-Gray-(light-mode)-600);
	--Colors-Text-text-tertiary_hover: var(--Colors-Gray-(light-mode)-700);
	--Colors-Text-text-tertiary_on-brand: var(--Colors-Brand-200);
	--Colors-Text-text-warning-primary-(600): var(--Colors-Warning-600);
	--Colors-Text-text-white: var(--Colors-Base-white);
	--Component-colors-Alpha-alpha-black-10: rgb(0 0 0 / 10%);
	--Component-colors-Alpha-alpha-black-100: rgb(0 0 0);
	--Component-colors-Alpha-alpha-black-20: rgb(0 0 0 / 20%);
	--Component-colors-Alpha-alpha-black-30: rgb(0 0 0 / 30%);
	--Component-colors-Alpha-alpha-black-40: rgb(0 0 0 / 40%);
	--Component-colors-Alpha-alpha-black-50: rgb(0 0 0 / 50%);
	--Component-colors-Alpha-alpha-black-60: rgb(0 0 0 / 60%);
	--Component-colors-Alpha-alpha-black-70: rgb(0 0 0 / 70%);
	--Component-colors-Alpha-alpha-black-80: rgb(0 0 0 / 80%);
	--Component-colors-Alpha-alpha-black-90: rgb(0 0 0 / 90%);
	--Component-colors-Alpha-alpha-white-10: rgb(255 255 255 / 10%);
	--Component-colors-Alpha-alpha-white-100: rgb(255 255 255);
	--Component-colors-Alpha-alpha-white-20: rgb(255 255 255 / 20%);
	--Component-colors-Alpha-alpha-white-30: rgb(255 255 255 / 30%);
	--Component-colors-Alpha-alpha-white-40: rgb(255 255 255 / 40%);
	--Component-colors-Alpha-alpha-white-50: rgb(255 255 255 / 50%);
	--Component-colors-Alpha-alpha-white-60: rgb(255 255 255 / 60%);
	--Component-colors-Alpha-alpha-white-70: rgb(255 255 255 / 70%);
	--Component-colors-Alpha-alpha-white-80: rgb(255 255 255 / 80%);
	--Component-colors-Alpha-alpha-white-90: rgb(255 255 255 / 90%);
	--Component-colors-Components-App-store-badges-app-store-badge-border: rgb(166 166 166);
	--Component-colors-Components-Avatars-avatar-styles-bg-neutral: rgb(224 224 224);
	--Component-colors-Components-Buttons-button-destructive-primary-icon: var(--Colors-Error-300);
	--Component-colors-Components-Buttons-button-destructive-primary-icon_hover: var(--Colors-Error-200);
	--Component-colors-Components-Buttons-button-primary-icon: var(--Colors-Brand-300);
	--Component-colors-Components-Buttons-button-primary-icon_hover: var(--Colors-Brand-200);
	--Component-colors-Components-Footers-footer-button-fg: var(--Colors-Brand-200);
	--Component-colors-Components-Footers-footer-button-fg_hover: var(--Colors-Base-white);
	--Component-colors-Components-Icons-Featured-icons-featured-icon-light-fg-brand: var(--Colors-Brand-600);
	--Component-colors-Components-Icons-Featured-icons-featured-icon-light-fg-error: var(--Colors-Error-600);
	--Component-colors-Components-Icons-Featured-icons-featured-icon-light-fg-gray: var(--Colors-Gray-(light-mode)-500);
	--Component-colors-Components-Icons-Featured-icons-featured-icon-light-fg-success: var(--Colors-Success-600);
	--Component-colors-Components-Icons-Featured-icons-featured-icon-light-fg-warning: var(--Colors-Warning-600);
	--Component-colors-Components-Icons-Icons-icon-fg-brand: var(--Colors-Brand-600);
	--Component-colors-Components-Icons-Icons-icon-fg-brand_on-brand: var(--Colors-Brand-200);
	--Component-colors-Components-Mockups-screen-mockup-border: var(--Colors-Gray-(light-mode)-900);
	--Component-colors-Components-Sliders-slider-handle-bg: var(--Colors-Base-white);
	--Component-colors-Components-Sliders-slider-handle-border: var(--Colors-Brand-600);
	--Component-colors-Components-Text-editor-text-editor-icon-fg: var(--Colors-Gray-(light-mode)-400);
	--Component-colors-Components-Text-editor-text-editor-icon-fg_active: var(--Colors-Gray-(light-mode)-500);
	--Component-colors-Components-Toggles-toggle-border: var(--Colors-Gray-(light-mode)-300);
	--Component-colors-Components-Toggles-toggle-button-fg_disabled: var(--Colors-Gray-(light-mode)-50);
	--Component-colors-Components-Toggles-toggle-slim-border_pressed: var(--Colors-Background-bg-brand-solid);
	--Component-colors-Components-Toggles-toggle-slim-border_pressed-hover: var(--Colors-Background-bg-brand-solid_hover);
	--Component-colors-Components-Tooltips-tooltip-supporting-text: var(--Colors-Gray-(light-mode)-300);
	--Component-colors-Utility-Blue-dark-utility-blue-dark-100: var(--Colors-Blue-dark-100);
	--Component-colors-Utility-Blue-dark-utility-blue-dark-200: var(--Colors-Blue-dark-200);
	--Component-colors-Utility-Blue-dark-utility-blue-dark-300: var(--Colors-Blue-dark-300);
	--Component-colors-Utility-Blue-dark-utility-blue-dark-400: var(--Colors-Blue-dark-400);
	--Component-colors-Utility-Blue-dark-utility-blue-dark-50: var(--Colors-Blue-dark-50);
	--Component-colors-Utility-Blue-dark-utility-blue-dark-500: var(--Colors-Blue-dark-500);
	--Component-colors-Utility-Blue-dark-utility-blue-dark-600: var(--Colors-Blue-dark-600);
	--Component-colors-Utility-Blue-dark-utility-blue-dark-700: var(--Colors-Blue-dark-700);
	--Component-colors-Utility-Blue-light-utility-blue-light-100: var(--Colors-Blue-light-100);
	--Component-colors-Utility-Blue-light-utility-blue-light-200: var(--Colors-Blue-light-200);
	--Component-colors-Utility-Blue-light-utility-blue-light-300: var(--Colors-Blue-light-300);
	--Component-colors-Utility-Blue-light-utility-blue-light-400: var(--Colors-Blue-light-400);
	--Component-colors-Utility-Blue-light-utility-blue-light-50: var(--Colors-Blue-light-50);
	--Component-colors-Utility-Blue-light-utility-blue-light-500: var(--Colors-Blue-light-500);
	--Component-colors-Utility-Blue-light-utility-blue-light-600: var(--Colors-Blue-light-600);
	--Component-colors-Utility-Blue-light-utility-blue-light-700: var(--Colors-Blue-light-700);
	--Component-colors-Utility-Blue-utility-blue-100: var(--Colors-Blue-100);
	--Component-colors-Utility-Blue-utility-blue-200: var(--Colors-Blue-200);
	--Component-colors-Utility-Blue-utility-blue-300: var(--Colors-Blue-300);
	--Component-colors-Utility-Blue-utility-blue-400: var(--Colors-Blue-400);
	--Component-colors-Utility-Blue-utility-blue-50: var(--Colors-Blue-50);
	--Component-colors-Utility-Blue-utility-blue-500: var(--Colors-Blue-500);
	--Component-colors-Utility-Blue-utility-blue-600: var(--Colors-Blue-600);
	--Component-colors-Utility-Blue-utility-blue-700: var(--Colors-Blue-700);
	--Component-colors-Utility-Brand-utility-brand-100: var(--Colors-Brand-100);
	--Component-colors-Utility-Brand-utility-brand-100_alt: var(--Colors-Brand-100);
	--Component-colors-Utility-Brand-utility-brand-200: var(--Colors-Brand-200);
	--Component-colors-Utility-Brand-utility-brand-200_alt: var(--Colors-Brand-200);
	--Component-colors-Utility-Brand-utility-brand-300: var(--Colors-Brand-300);
	--Component-colors-Utility-Brand-utility-brand-300_alt: var(--Colors-Brand-300);
	--Component-colors-Utility-Brand-utility-brand-400: var(--Colors-Brand-400);
	--Component-colors-Utility-Brand-utility-brand-400_alt: var(--Colors-Brand-400);
	--Component-colors-Utility-Brand-utility-brand-50: var(--Colors-Brand-50);
	--Component-colors-Utility-Brand-utility-brand-500: var(--Colors-Brand-500);
	--Component-colors-Utility-Brand-utility-brand-500_alt: var(--Colors-Brand-500);
	--Component-colors-Utility-Brand-utility-brand-50_alt: var(--Colors-Brand-50);
	--Component-colors-Utility-Brand-utility-brand-600: var(--Colors-Brand-600);
	--Component-colors-Utility-Brand-utility-brand-600_alt: var(--Colors-Brand-600);
	--Component-colors-Utility-Brand-utility-brand-700: var(--Colors-Brand-700);
	--Component-colors-Utility-Brand-utility-brand-700_alt: var(--Colors-Brand-700);
	--Component-colors-Utility-Brand-utility-brand-800: var(--Colors-Brand-800);
	--Component-colors-Utility-Brand-utility-brand-800_alt: var(--Colors-Brand-800);
	--Component-colors-Utility-Brand-utility-brand-900: var(--Colors-Brand-900);
	--Component-colors-Utility-Brand-utility-brand-900_alt: var(--Colors-Brand-900);
	--Component-colors-Utility-Error-utility-error-100: var(--Colors-Error-100);
	--Component-colors-Utility-Error-utility-error-200: var(--Colors-Error-200);
	--Component-colors-Utility-Error-utility-error-300: var(--Colors-Error-300);
	--Component-colors-Utility-Error-utility-error-400: var(--Colors-Error-400);
	--Component-colors-Utility-Error-utility-error-50: var(--Colors-Error-50);
	--Component-colors-Utility-Error-utility-error-500: var(--Colors-Error-500);
	--Component-colors-Utility-Error-utility-error-600: var(--Colors-Error-600);
	--Component-colors-Utility-Error-utility-error-700: var(--Colors-Error-700);
	--Component-colors-Utility-Fuchsia-utility-fuchsia-100: var(--Colors-Fuchsia-100);
	--Component-colors-Utility-Fuchsia-utility-fuchsia-200: var(--Colors-Fuchsia-200);
	--Component-colors-Utility-Fuchsia-utility-fuchsia-300: var(--Colors-Fuchsia-300);
	--Component-colors-Utility-Fuchsia-utility-fuchsia-400: var(--Colors-Fuchsia-400);
	--Component-colors-Utility-Fuchsia-utility-fuchsia-50: var(--Colors-Fuchsia-50);
	--Component-colors-Utility-Fuchsia-utility-fuchsia-500: var(--Colors-Fuchsia-500);
	--Component-colors-Utility-Fuchsia-utility-fuchsia-600: var(--Colors-Fuchsia-600);
	--Component-colors-Utility-Fuchsia-utility-fuchsia-700: var(--Colors-Fuchsia-700);
	--Component-colors-Utility-Gray-blue-utility-gray-blue-100: var(--Colors-Gray-blue-100);
	--Component-colors-Utility-Gray-blue-utility-gray-blue-200: var(--Colors-Gray-blue-200);
	--Component-colors-Utility-Gray-blue-utility-gray-blue-300: var(--Colors-Gray-blue-300);
	--Component-colors-Utility-Gray-blue-utility-gray-blue-400: var(--Colors-Gray-blue-400);
	--Component-colors-Utility-Gray-blue-utility-gray-blue-50: var(--Colors-Gray-blue-50);
	--Component-colors-Utility-Gray-blue-utility-gray-blue-500: var(--Colors-Gray-blue-500);
	--Component-colors-Utility-Gray-blue-utility-gray-blue-600: var(--Colors-Gray-blue-600);
	--Component-colors-Utility-Gray-blue-utility-gray-blue-700: var(--Colors-Gray-blue-700);
	--Component-colors-Utility-Gray-utility-gray-100: var(--Colors-Gray-(light-mode)-100);
	--Component-colors-Utility-Gray-utility-gray-200: var(--Colors-Gray-(light-mode)-200);
	--Component-colors-Utility-Gray-utility-gray-300: var(--Colors-Gray-(light-mode)-300);
	--Component-colors-Utility-Gray-utility-gray-400: var(--Colors-Gray-(light-mode)-400);
	--Component-colors-Utility-Gray-utility-gray-50: var(--Colors-Gray-(light-mode)-50);
	--Component-colors-Utility-Gray-utility-gray-500: var(--Colors-Gray-(light-mode)-500);
	--Component-colors-Utility-Gray-utility-gray-600: var(--Colors-Gray-(light-mode)-600);
	--Component-colors-Utility-Gray-utility-gray-700: var(--Colors-Gray-(light-mode)-700);
	--Component-colors-Utility-Gray-utility-gray-800: var(--Colors-Gray-(light-mode)-800);
	--Component-colors-Utility-Gray-utility-gray-900: var(--Colors-Gray-(light-mode)-900);
	--Component-colors-Utility-Green-utility-green-100: var(--Colors-Green-100);
	--Component-colors-Utility-Green-utility-green-200: var(--Colors-Green-200);
	--Component-colors-Utility-Green-utility-green-300: var(--Colors-Green-300);
	--Component-colors-Utility-Green-utility-green-400: var(--Colors-Green-400);
	--Component-colors-Utility-Green-utility-green-50: var(--Colors-Green-50);
	--Component-colors-Utility-Green-utility-green-500: var(--Colors-Green-500);
	--Component-colors-Utility-Green-utility-green-600: var(--Colors-Green-600);
	--Component-colors-Utility-Green-utility-green-700: var(--Colors-Green-700);
	--Component-colors-Utility-Indigo-utility-indigo-100: var(--Colors-Indigo-100);
	--Component-colors-Utility-Indigo-utility-indigo-200: var(--Colors-Indigo-200);
	--Component-colors-Utility-Indigo-utility-indigo-300: var(--Colors-Indigo-300);
	--Component-colors-Utility-Indigo-utility-indigo-400: var(--Colors-Indigo-400);
	--Component-colors-Utility-Indigo-utility-indigo-50: var(--Colors-Indigo-50);
	--Component-colors-Utility-Indigo-utility-indigo-500: var(--Colors-Indigo-500);
	--Component-colors-Utility-Indigo-utility-indigo-600: var(--Colors-Indigo-600);
	--Component-colors-Utility-Indigo-utility-indigo-700: var(--Colors-Indigo-700);
	--Component-colors-Utility-Orange-dark-utility-orange-dark-100: var(--Colors-Orange-dark-100);
	--Component-colors-Utility-Orange-dark-utility-orange-dark-200: var(--Colors-Orange-dark-200);
	--Component-colors-Utility-Orange-dark-utility-orange-dark-300: var(--Colors-Orange-dark-300);
	--Component-colors-Utility-Orange-dark-utility-orange-dark-400: var(--Colors-Orange-dark-400);
	--Component-colors-Utility-Orange-dark-utility-orange-dark-50: var(--Colors-Orange-dark-50);
	--Component-colors-Utility-Orange-dark-utility-orange-dark-500: var(--Colors-Orange-dark-500);
	--Component-colors-Utility-Orange-dark-utility-orange-dark-600: var(--Colors-Orange-dark-600);
	--Component-colors-Utility-Orange-dark-utility-orange-dark-700: var(--Colors-Orange-dark-700);
	--Component-colors-Utility-Orange-utility-orange-100: var(--Colors-Orange-100);
	--Component-colors-Utility-Orange-utility-orange-200: var(--Colors-Orange-200);
	--Component-colors-Utility-Orange-utility-orange-300: var(--Colors-Orange-300);
	--Component-colors-Utility-Orange-utility-orange-400: var(--Colors-Orange-400);
	--Component-colors-Utility-Orange-utility-orange-50: var(--Colors-Orange-50);
	--Component-colors-Utility-Orange-utility-orange-500: var(--Colors-Orange-500);
	--Component-colors-Utility-Orange-utility-orange-600: var(--Colors-Orange-600);
	--Component-colors-Utility-Orange-utility-orange-700: var(--Colors-Orange-700);
	--Component-colors-Utility-Pink-utility-pink-100: var(--Colors-Pink-100);
	--Component-colors-Utility-Pink-utility-pink-200: var(--Colors-Pink-200);
	--Component-colors-Utility-Pink-utility-pink-300: var(--Colors-Pink-300);
	--Component-colors-Utility-Pink-utility-pink-400: var(--Colors-Pink-400);
	--Component-colors-Utility-Pink-utility-pink-50: var(--Colors-Pink-50);
	--Component-colors-Utility-Pink-utility-pink-500: var(--Colors-Pink-500);
	--Component-colors-Utility-Pink-utility-pink-600: var(--Colors-Pink-600);
	--Component-colors-Utility-Pink-utility-pink-700: var(--Colors-Pink-700);
	--Component-colors-Utility-Purple-utility-purple-100: var(--Colors-Purple-100);
	--Component-colors-Utility-Purple-utility-purple-200: var(--Colors-Purple-200);
	--Component-colors-Utility-Purple-utility-purple-300: var(--Colors-Purple-300);
	--Component-colors-Utility-Purple-utility-purple-400: var(--Colors-Purple-400);
	--Component-colors-Utility-Purple-utility-purple-50: var(--Colors-Purple-50);
	--Component-colors-Utility-Purple-utility-purple-500: var(--Colors-Purple-500);
	--Component-colors-Utility-Purple-utility-purple-600: var(--Colors-Purple-600);
	--Component-colors-Utility-Purple-utility-purple-700: var(--Colors-Purple-700);
	--Component-colors-Utility-Success-utility-success-100: var(--Colors-Success-100);
	--Component-colors-Utility-Success-utility-success-200: var(--Colors-Success-200);
	--Component-colors-Utility-Success-utility-success-300: var(--Colors-Success-300);
	--Component-colors-Utility-Success-utility-success-400: var(--Colors-Success-400);
	--Component-colors-Utility-Success-utility-success-50: var(--Colors-Success-50);
	--Component-colors-Utility-Success-utility-success-500: var(--Colors-Success-500);
	--Component-colors-Utility-Success-utility-success-600: var(--Colors-Success-600);
	--Component-colors-Utility-Success-utility-success-700: var(--Colors-Success-700);
	--Component-colors-Utility-Warning-utility-warning-100: var(--Colors-Warning-100);
	--Component-colors-Utility-Warning-utility-warning-200: var(--Colors-Warning-200);
	--Component-colors-Utility-Warning-utility-warning-300: var(--Colors-Warning-300);
	--Component-colors-Utility-Warning-utility-warning-400: var(--Colors-Warning-400);
	--Component-colors-Utility-Warning-utility-warning-50: var(--Colors-Warning-50);
	--Component-colors-Utility-Warning-utility-warning-500: var(--Colors-Warning-500);
	--Component-colors-Utility-Warning-utility-warning-600: var(--Colors-Warning-600);
	--Component-colors-Utility-Warning-utility-warning-700: var(--Colors-Warning-700);
	--Component-colors-Utility-Yellow-utility-yellow-100: var(--Colors-Yellow-100);
	--Component-colors-Utility-Yellow-utility-yellow-200: var(--Colors-Yellow-200);
	--Component-colors-Utility-Yellow-utility-yellow-300: var(--Colors-Yellow-300);
	--Component-colors-Utility-Yellow-utility-yellow-400: var(--Colors-Yellow-400);
	--Component-colors-Utility-Yellow-utility-yellow-50: var(--Colors-Yellow-50);
	--Component-colors-Utility-Yellow-utility-yellow-500: var(--Colors-Yellow-500);
	--Component-colors-Utility-Yellow-utility-yellow-600: var(--Colors-Yellow-600);
	--Component-colors-Utility-Yellow-utility-yellow-700: var(--Colors-Yellow-700);
}
