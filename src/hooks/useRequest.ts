"use client";

import use<PERSON><PERSON> from "swr";
import { useRouter } from "next/navigation";
import { fetcher } from "@/lib/common/requests";
import { toast } from "sonner";
import { useCallback, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import {
  searchRequests,
  getRequest,
  createRequest,
  updateRequest,
  deleteRequest,
  performBulkAction,
  getRequestStatistics,
  type CreateRequest,
  type UpdateRequest,
  type BulkRequest,
  type RequestSearchParams,
} from "@/store/actions/request";
import {
  selectRequests,
  selectCurrentRequest,
  selectRequestStatistics,
  selectIsLoading,
  selectIsCreating,
  selectIsUpdating,
  selectIsDeleting,
  selectIsBulkActionLoading,
  selectIsStatisticsLoading,
  selectError,
  selectSearchQuery,
  selectPagination,
  selectFilters,
  clearError,
  setLoading,
  setCreating,
  setUpdating,
  setDeleting,
  setBulkActionLoading,
  setError,
  setCurrentRequest,
  setSearchQuery,
  setFilters,
  clearFilters,
  resetState,
} from "@/store/slices/request";
import type { Request } from "@/lib/api/validators/schemas/request";
import { usePagination } from "./usePagination";

/**
 * useRequest Hook
 * 
 * Provides comprehensive request management functionality including:
 * - CRUD operations with Redux state management
 * - SWR integration for data fetching and caching
 * - Search and filtering capabilities
 * - Bulk operations
 * - Statistics and analytics
 * - Error handling and loading states
 */
export function useRequest() {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();

  // Local state
  const [requests, setRequests] = useState<Request[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<Request[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  // Redux selectors
  const reduxRequests = useSelector(selectRequests);
  const currentRequest = useSelector(selectCurrentRequest);
  const statistics = useSelector(selectRequestStatistics);
  const isLoading = useSelector(selectIsLoading);
  const isCreating = useSelector(selectIsCreating);
  const isUpdating = useSelector(selectIsUpdating);
  const isDeleting = useSelector(selectIsDeleting);
  const isBulkActionLoading = useSelector(selectIsBulkActionLoading);
  const isStatisticsLoading = useSelector(selectIsStatisticsLoading);
  const error = useSelector(selectError);
  const searchQuery = useSelector(selectSearchQuery);
  const pagination = useSelector(selectPagination);
  const filters = useSelector(selectFilters);

  // SWR for data fetching
  const {
    data: swrRequests,
    error: swrError,
    mutate: mutateRequests,
    isLoading: swrLoading,
  } = useSWR<any>("/api/request", fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000,
  });

  const {
    data: swrStatistics,
    error: swrStatsError,
    mutate: mutateStatistics,
    isLoading: swrStatsLoading,
  } = useSWR<any>("/api/request/statistics", fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 30000, // Cache stats for 30 seconds
  });

  // Pagination hook
  const paginationHook = usePagination({
    initialPage: 1,
    initialLimit: 10,
  });

  // Sync SWR data with local state
  useEffect(() => {
    if (swrRequests?.success && swrRequests?.data) {
      setRequests(Array.isArray(swrRequests.data) ? swrRequests.data : []);
    }
  }, [swrRequests]);

  // Sync Redux data with local state
  useEffect(() => {
    if (reduxRequests.length > 0) {
      setRequests(reduxRequests);
    }
  }, [reduxRequests]);

  // Filter requests based on search term and filters
  useEffect(() => {
    let filtered = requests;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (request) =>
          request.message?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          request.tender_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
          request.user?.email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (filters.status) {
      filtered = filtered.filter((request) => request.status === filters.status);
    }

    // Apply user filter
    if (filters.userId) {
      filtered = filtered.filter((request) => request.userId === filters.userId);
    }

    // Apply tender filter
    if (filters.tender_id) {
      filtered = filtered.filter((request) => request.tender_id === filters.tender_id);
    }

    setFilteredRequests(filtered);
  }, [requests, searchTerm, filters]);

  // CRUD Operations
  const handleCreateRequest = useCallback(
    async (requestData: CreateRequest) => {
      try {
        dispatch(setCreating(true));
        const result = await dispatch(createRequest(requestData));
        
        if (createRequest.fulfilled.match(result)) {
          mutateRequests(); // Refresh SWR cache
          return { success: true, data: result.payload };
        } else {
          return { success: false, error: result.payload };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to create request";
        dispatch(setError(errorMessage));
        return { success: false, error: errorMessage };
      } finally {
        dispatch(setCreating(false));
      }
    },
    [dispatch, mutateRequests]
  );

  const handleUpdateRequest = useCallback(
    async (requestData: UpdateRequest) => {
      try {
        dispatch(setUpdating(true));
        const result = await dispatch(updateRequest(requestData));
        
        if (updateRequest.fulfilled.match(result)) {
          mutateRequests(); // Refresh SWR cache
          return { success: true, data: result.payload };
        } else {
          return { success: false, error: result.payload };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to update request";
        dispatch(setError(errorMessage));
        return { success: false, error: errorMessage };
      } finally {
        dispatch(setUpdating(false));
      }
    },
    [dispatch, mutateRequests]
  );

  const handleDeleteRequest = useCallback(
    async (requestId: string) => {
      try {
        dispatch(setDeleting(true));
        const result = await dispatch(deleteRequest(requestId));
        
        if (deleteRequest.fulfilled.match(result)) {
          mutateRequests(); // Refresh SWR cache
          return { success: true, data: result.payload };
        } else {
          return { success: false, error: result.payload };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to delete request";
        dispatch(setError(errorMessage));
        return { success: false, error: errorMessage };
      } finally {
        dispatch(setDeleting(false));
      }
    },
    [dispatch, mutateRequests]
  );

  const handleGetRequest = useCallback(
    async (requestId: string) => {
      try {
        dispatch(setLoading(true));
        const result = await dispatch(getRequest(requestId));
        
        if (getRequest.fulfilled.match(result)) {
          return { success: true, data: result.payload };
        } else {
          return { success: false, error: result.payload };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to fetch request";
        dispatch(setError(errorMessage));
        return { success: false, error: errorMessage };
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch]
  );

  // Search and filtering
  const handleSearchRequests = useCallback(
    async (searchParams: RequestSearchParams) => {
      try {
        dispatch(setLoading(true));
        const result = await dispatch(searchRequests(searchParams));
        
        if (searchRequests.fulfilled.match(result)) {
          mutateRequests(); // Refresh SWR cache
          return { success: true, data: result.payload };
        } else {
          return { success: false, error: result.payload };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to search requests";
        dispatch(setError(errorMessage));
        return { success: false, error: errorMessage };
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch, mutateRequests]
  );

  // Bulk operations
  const handleBulkAction = useCallback(
    async (bulkData: BulkRequest) => {
      try {
        dispatch(setBulkActionLoading(true));
        const result = await dispatch(performBulkAction(bulkData));
        
        if (performBulkAction.fulfilled.match(result)) {
          mutateRequests(); // Refresh SWR cache
          return { success: true, data: result.payload };
        } else {
          return { success: false, error: result.payload };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to perform bulk action";
        dispatch(setError(errorMessage));
        return { success: false, error: errorMessage };
      } finally {
        dispatch(setBulkActionLoading(false));
      }
    },
    [dispatch, mutateRequests]
  );

  // Statistics
  const handleGetStatistics = useCallback(
    async () => {
      try {
        const result = await dispatch(getRequestStatistics());
        
        if (getRequestStatistics.fulfilled.match(result)) {
          mutateStatistics(); // Refresh SWR cache
          return { success: true, data: result.payload };
        } else {
          return { success: false, error: result.payload };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to fetch statistics";
        dispatch(setError(errorMessage));
        return { success: false, error: errorMessage };
      }
    },
    [dispatch, mutateStatistics]
  );

  // Utility functions
  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const updateSearchTerm = useCallback((term: string) => {
    setSearchTerm(term);
    dispatch(setSearchQuery(term));
  }, [dispatch]);

  const updateFilters = useCallback((newFilters: Partial<typeof filters>) => {
    dispatch(setFilters(newFilters));
  }, [dispatch]);

  const clearAllFilters = useCallback(() => {
    setSearchTerm("");
    dispatch(clearFilters());
    dispatch(setSearchQuery(""));
  }, [dispatch]);

  const resetRequestState = useCallback(() => {
    dispatch(resetState());
    setRequests([]);
    setFilteredRequests([]);
    setSearchTerm("");
  }, [dispatch]);

  return {
    // Data
    requests: filteredRequests,
    allRequests: requests,
    currentRequest,
    statistics: statistics || swrStatistics?.data,
    
    // Loading states
    isLoading: isLoading || swrLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isBulkActionLoading,
    isStatisticsLoading: isStatisticsLoading || swrStatsLoading,
    
    // Error states
    error: error || swrError?.message || swrStatsError?.message,
    
    // Search and filtering
    searchTerm,
    searchQuery,
    filters,
    pagination,
    
    // Pagination
    ...paginationHook,
    
    // CRUD operations
    createRequest: handleCreateRequest,
    updateRequest: handleUpdateRequest,
    deleteRequest: handleDeleteRequest,
    getRequest: handleGetRequest,
    
    // Search and filtering
    searchRequests: handleSearchRequests,
    setSearchTerm: updateSearchTerm,
    setFilters: updateFilters,
    clearFilters: clearAllFilters,
    
    // Bulk operations
    performBulkAction: handleBulkAction,
    
    // Statistics
    getStatistics: handleGetStatistics,
    
    // Utility functions
    clearError: clearErrorState,
    resetState: resetRequestState,
    refreshData: mutateRequests,
    refreshStatistics: mutateStatistics,
    
    // Redux actions for direct use
    setCurrentRequest: (request: Request | null) => dispatch(setCurrentRequest(request)),
  };
}
