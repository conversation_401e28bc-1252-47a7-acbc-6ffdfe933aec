# Dynamic Offset Usage Examples

This document demonstrates how to use the new dynamic offset functionality in the NestTenderService.

## Overview

The service now includes methods to automatically calculate the appropriate offset for external API calls based on existing records in the database. This helps avoid duplicate data and ensures efficient pagination when seeding data.

## New Methods Added

### BaseService Methods

1. **`getLastModelRowIndex(beforeDate?, createdAtField?)`**
   - Counts records created before a specific date
   - Returns the count which can be used as an offset

2. **`getLastModelRowIndexByDays(daysSince?, createdAtField?)`**
   - Counts records created within a specific day range
   - More convenient for time-based pagination

### NestTenderService Methods

1. **`getSeedingParams(customOffset?, daysSince?, useDynamicOffset?)`**
   - Generates URL parameters with dynamic offset calculation
   - Can use existing record count as offset

2. **`seedingBaseURL(customOffset?, daysSince?, useDynamicOffset?)`**
   - Combines base URL with dynamic parameters
   - Ready-to-use URL for external API calls

## Usage Examples

### Example 1: Basic Dynamic Offset

```typescript
// The service will automatically calculate offset based on existing records
const tenderService = new NestTenderService();

// This will:
// 1. Count existing tender records from the last 30 days
// 2. Use that count as the offset
// 3. Generate URL like: https://api.example.com/tenders?offset=150&since=2024-08-17T10:30:00Z
const result = await tenderService.bulkSeedFromExternalApi();
```

### Example 2: Custom Time Range

```typescript
const tenderService = new NestTenderService();

// Get offset for last 7 days of records
const offset = await tenderService.getLastModelRowIndexByDays(7);
console.log(`Found ${offset} records from last 7 days`);

// Use custom parameters
const url = await tenderService.seedingBaseURL(null, 7, true);
// Result: https://api.example.com/tenders?offset=45&since=2024-09-10T10:30:00Z
```

### Example 3: Manual Offset Override

```typescript
const tenderService = new NestTenderService();

// Force specific offset (disable dynamic calculation)
const url = await tenderService.seedingBaseURL(100, 30, false);
// Result: https://api.example.com/tenders?offset=100&since=2024-08-17T10:30:00Z
```

### Example 4: Different Date Field

```typescript
const tenderService = new NestTenderService();

// If your model uses a different timestamp field
const offset = await tenderService.getLastModelRowIndex(
  new Date('2024-01-01'), 
  'updatedAt'  // Use updatedAt instead of createdAt
);
```

## API Integration

### Before (Static Offset)
```typescript
// Old way - always started from offset 0
const url = `${process.env.NEST_API_URL}?offset=0&since=2024-08-17T10:30:00Z`;
```

### After (Dynamic Offset)
```typescript
// New way - automatically calculates appropriate offset
const tenderService = new NestTenderService();
const url = await tenderService.seedingBaseURL();
// Result: https://api.example.com/tenders?offset=247&since=2024-08-17T10:30:00Z
```

## Benefits

1. **Avoids Duplicates**: By starting from the last known record, reduces duplicate data
2. **Efficient Pagination**: Only fetches new records since last sync
3. **Flexible Time Ranges**: Can adjust the time window for different use cases
4. **Fallback Safety**: If calculation fails, falls back to offset 0
5. **Customizable**: Can override with manual offset when needed

## Error Handling

The methods include comprehensive error handling:

```typescript
try {
  const offset = await tenderService.getLastModelRowIndexByDays(30);
  console.log(`Using offset: ${offset}`);
} catch (error) {
  console.warn('Failed to calculate offset, using 0:', error.message);
  // Service automatically falls back to offset 0
}
```

## Logging

The service provides detailed logging for debugging:

```
INFO: Getting last model row index for last 30 days
INFO: Searching for records created between 2024-08-17T10:30:00Z and 2024-09-16T10:30:00Z
INFO: Found 247 records created within the last 30 days
INFO: Calculated dynamic offset: 247 based on existing records
INFO: Generated seeding params: offset=247, since=2024-08-17T10:30:00Z
INFO: Generated seeding URL: https://api.example.com/tenders?offset=247&since=2024-08-17T10:30:00Z
```

## Configuration Options

### Default Behavior
- `daysSince`: 30 days
- `useDynamicOffset`: true
- `customOffset`: null (calculated automatically)
- `createdAtField`: 'createdAt'

### Customization
```typescript
// Custom configuration
const params = await tenderService.getSeedingParams(
  null,    // customOffset - let it calculate
  14,      // daysSince - last 2 weeks
  true     // useDynamicOffset - enable dynamic calculation
);
```

This dynamic offset functionality ensures that your data seeding operations are efficient, avoid duplicates, and can resume from the appropriate point in your dataset.
