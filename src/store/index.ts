import { configureStore, combineReducers } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import {
  authSlice,
  proposalSlice,
  contractSlice,
  requestSlice,
} from "./slices";
import documentsReducer from "./slices/document";
import chatReducer from "./slices/chat";
import dashboardReducer from "./slices/dashboard";
import braintreeReducer from "./slices/braintree";
import paginationReducer from "./slices/pagination";
import settingsReducer from "./slices/settings";

// Combine all reducers
const rootReducer = combineReducers({
  auth: authSlice.reducer,
  proposal: proposalSlice.reducer,
  contracts: contractSlice.reducer,
  request: requestSlice.reducer,
  documents: documentsReducer,
  chat: chatReducer.reducer,
  dashboard: dashboardReducer,
  braintree: braintreeReducer.reducer,
  pagination: paginationReducer,
  settings: settingsReducer,
});

// Persist configuration
const persistConfig = {
  key: "root",
  storage,
  whitelist: ["auth", "pagination", "settings"], // Persist auth, pagination, and settings slices
  version: 1,
};

// Create persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
      },
    }),
  devTools: process.env.NODE_ENV !== "production",
});

// Create persistor
export const persistor = persistStore(store);

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
